<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Your Job Post - GigGenius</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/page1-2025.css') }}">
    <style>
        /* Back Button Style */
        .back-button {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            color: #4b5563;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 6px;
            margin-bottom: 12px;
            width: fit-content;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }
        
        .back-button:hover {
            background-color: #f3f4f6;
            color: #2563eb;
            border-color: #e5e7eb;
        }
        
        .back-button i {
            margin-right: 6px;
            font-size: 12px;
        }
        
        /* Professional Enterprise UI - Core Styles */
        :root {
            --primary: #2563eb;
            --primary-light: #dbeafe;
            --primary-dark: #1e40af;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --radius-sm: 0.125rem;
            --radius: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
        }
        
        body {
            font-size: 14px;
            margin: 0;
            padding: 0;
            font-family: 'Poppins', sans-serif;
            color: var(--gray-800);
            background-color: #f8fafc;
            line-height: 1.5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 12px;
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow);
        }
        
        /* Progress bar */
        .progress-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            padding: 8px 0;
        }
        
        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 80px;
            position: relative;
            z-index: 1;
        }
        
        .step-number {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 13px;
            background-color: var(--gray-200);
            color: var(--gray-600);
            position: relative;
            z-index: 2;
            border: 2px solid white;
            box-shadow: var(--shadow-sm);
        }
        
        .step-number.active {
            background-color: var(--primary);
            color: white;
        }
        
        .step-label {
            font-size: 12px;
            margin-top: 4px;
            font-weight: 500;
            color: var(--gray-500);
        }
        
        .step-label.active {
            color: var(--primary);
            font-weight: 600;
        }
        
        .step-connector {
            height: 3px;
            width: 60px;
            background-color: var(--gray-200);
            position: relative;
        }
        
        /* Header section */
        .header-section {
            text-align: center;
            margin-bottom: 16px;
            padding: 0 10px;
            position: relative;
        }
        
        .header-section::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background-color: var(--primary);
            border-radius: var(--radius);
        }
        
        .title-section h1 {
            margin: 0 0 6px 0;
            font-size: 1.5em;
            font-weight: 700;
            color: var(--gray-900);
            letter-spacing: -0.01em;
        }
        
        .title-section p {
            font-size: 0.9em;
            margin: 0;
            color: var(--gray-600);
        }
    </style>
</head>
<body>
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        <i class="fas fa-info-circle"></i>
                        {{ message }}
                        <button type="button" class="alert-close" onclick="this.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <form action="/save_page1" method="POST" id="jobTypeForm">
        <div class="container">
            <!-- Back Button -->
            <div class="back-button" onclick="goBack()">
                <i class="fas fa-arrow-left"></i> Back
            </div>
            
            <!-- Progress Indicator -->
            <div class="progress-indicator">
                <div class="progress-step">
                    <div class="step-number active">1</div>
                    <div class="step-label active">Job Type</div>
                </div>
                <div class="step-connector"></div>
                <div class="progress-step">
                    <div class="step-number">2</div>
                    <div class="step-label">Job Details</div>
                </div>
                <div class="step-connector"></div>
                <div class="progress-step">
                    <div class="step-number">3</div>
                    <div class="step-label">Review & Post</div>
                </div>
            </div>

            <!-- Header Section -->
            <div class="header-section">
                <div class="title-section">
                    <h1>Let's create your perfect job post</h1>
                    <p>Choose the option that best fits your needs and we'll guide you through the process</p>
                </div>

                <!-- Quick Stats -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <i class="fas fa-users"></i>
                        <span>10,000+ Talented Professionals</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>Average Response: 2 Hours</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span>4.9/5 Average Rating</span>
                    </div>
                </div>
            </div>
            <!-- Main Content Section -->
            <div class="main-content">
                <!-- Primary Options -->
                <div class="options-section">
                    <h2 class="section-title">
                        <i class="fas fa-plus-circle"></i>
                        What would you like to do?
                    </h2>

                    <!-- Create New Job -->
                    <div class="primary-option-card" id="newJobCard">
                        <div class="option-header">
                            <div class="option-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="option-content">
                                <h3>Create a New Job Post</h3>
                                <p>Start fresh with a brand new job posting</p>
                            </div>
                            <div class="option-toggle">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>

                        <div class="option-details compact-view" id="newJobDetails">
                            <div class="selection-instructions">
                                <i class="fas fa-info-circle"></i>
                                <p>Select a job type that best fits your needs:</p>
                            </div>
                            <div class="job-type-selection">
                                <div class="job-type-card compact" onclick="selectJobType('one-time', this)" title="Best for projects with a clear end goal and timeline">
                                    <input type="radio" name="job_type" value="one-time" hidden>
                                    <div class="job-type-icon">
                                        <i class="fas fa-calendar-day"></i>
                                    </div>
                                    <div class="job-type-info">
                                        <h4>One-time Project</h4>
                                        <p>Single project with defined scope and timeline</p>
                                        <div class="job-type-features">
                                            <span><i class="fas fa-check"></i> Fixed scope</span>
                                            <span><i class="fas fa-bullseye"></i> Clear deliverables</span>
                                            <span><i class="fas fa-calendar-check"></i> Set deadline</span>
                                        </div>
                                    </div>
                                    <div class="selection-indicator">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                </div>

                                <div class="job-type-card compact" onclick="selectJobType('ongoing', this)" title="Best for projects requiring continuous work or regular updates">
                                    <input type="radio" name="job_type" value="ongoing" hidden>
                                    <div class="job-type-icon">
                                        <i class="fas fa-sync"></i>
                                    </div>
                                    <div class="job-type-info">
                                        <h4>Ongoing Collaboration</h4>
                                        <p>Long-term partnership with flexible schedule</p>
                                        <div class="job-type-features">
                                            <span><i class="fas fa-check"></i> Flexible hours</span>
                                            <span><i class="fas fa-handshake"></i> Long-term</span>
                                            <span><i class="fas fa-clock"></i> Hourly/retainer</span>
                                        </div>
                                    </div>
                                    <div class="selection-indicator">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Continue Draft -->
                    {% if draft_jobs %}
                    <div class="primary-option-card" id="draftCard">
                        <div class="option-header">
                            <div class="option-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="option-content">
                                <h3>Continue Draft</h3>
                                <p>Resume working on a saved draft ({{ draft_jobs|length }} available)</p>
                            </div>
                            <div class="option-toggle">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>

                        <div class="option-details compact-view" id="draftDetails">
                            <div class="draft-list">
                                {% for draft in draft_jobs %}
                                <div class="draft-item compact" onclick="selectDraft(this, '{{ draft.title }}', {{ draft.id }})">
                                    <div class="draft-info">
                                        <h4>{{ draft.title }}</h4>
                                        <p class="draft-date">
                                            <i class="fas fa-calendar"></i>
                                            Created {{ draft.created_at.strftime('%b %d, %Y') if draft.created_at else 'Recently' }}
                                        </p>
                                    </div>
                                    <div class="draft-action">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Rework Previous Job -->
                    <div class="primary-option-card" id="reworkCard">
                        <div class="option-header">
                            <div class="option-icon">
                                <i class="fas fa-redo"></i>
                            </div>
                            <div class="option-content">
                                <h3>Rework a Previous Job</h3>
                                <p>Repost or modify a paused or completed job posting</p>
                            </div>
                            <div class="option-toggle">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>

                        <div class="option-details compact-view" id="reworkDetails">
                            <div class="rework-list">
                                <!-- This will be populated with paused/completed jobs from the database -->
                                <div class="rework-item compact" onclick="selectRework(this, 'Web Developer Position', 'paused', 123)">
                                    <div class="rework-info">
                                        <h4>Web Developer Position</h4>
                                        <p class="rework-status">
                                            <i class="fas fa-pause-circle"></i>
                                            Status: Paused • Posted 2 weeks ago
                                        </p>
                                    </div>
                                    <div class="rework-action">
                                        <span class="rework-badge paused">Paused</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </div>

                                <div class="rework-item compact" onclick="selectRework(this, 'UI/UX Designer', 'completed', 124)">
                                    <div class="rework-info">
                                        <h4>UI/UX Designer</h4>
                                        <p class="rework-status">
                                            <i class="fas fa-check-circle"></i>
                                            Status: Completed • Posted 1 month ago
                                        </p>
                                    </div>
                                    <div class="rework-action">
                                        <span class="rework-badge completed">Completed</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </div>

                                <div class="rework-item compact" onclick="selectRework(this, 'Content Writer', 'paused', 125)">
                                    <div class="rework-info">
                                        <h4>Content Writer</h4>
                                        <p class="rework-status">
                                            <i class="fas fa-pause-circle"></i>
                                            Status: Paused • Posted 5 days ago
                                        </p>
                                    </div>
                                    <div class="rework-action">
                                        <span class="rework-badge paused">Paused</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </div>

                                <!-- If no paused/completed jobs -->
                                <div class="no-rework-jobs" style="display: none;">
                                    <i class="fas fa-info-circle"></i>
                                    <p>No paused or completed jobs available to rework.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
            <!-- Action Buttons -->
            <div class="action-section">
                <div class="action-buttons">
                    <button type="button" class="secondary-button" onclick="cancelProcess()">
                        <i class="fas fa-times"></i>
                        Cancel
                    </button>
                    <button type="button" class="primary-button disabled" id="continueButton" onclick="handleContinue()">
                        <span class="button-text">Continue</span>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <!-- Selection Summary -->
                <div class="selection-summary" id="selectionSummary" style="display: none;">
                    <div class="summary-content">
                        <i class="fas fa-check-circle"></i>
                        <span id="summaryText">Ready to continue</span>
                    </div>
                </div>
            </div>

            <!-- Hidden inputs -->
            <input type="hidden" id="selectedDraftId" name="draft_job_id" value="">
            <input type="hidden" id="selectedMode" name="mode" value="">
            <input type="hidden" id="selectedRework" name="rework_job_id" value="">

            <!-- Fallback for browsers with JavaScript disabled -->
            <noscript>
                <div class="noscript-fallback">
                    <div class="noscript-content">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>JavaScript Required</h3>
                        <p>Please enable JavaScript for the best experience with GigGenius.</p>
                        <button type="submit" class="noscript-button">
                            Continue Anyway
                        </button>
                    </div>
                </div>
            </noscript>
        </div>
    </form>





    <script>
        // Function to handle back button click
        function goBack() {
            // Check if there's a previous page in history
            if (document.referrer) {
                window.location.href = document.referrer;
            } else {
                // If no referrer, go to default page
                window.location.href = '/client_page';
            }
        }
        
        // Global variables
        let selectedOption = null;
        let selectedJobType = null;
        let selectedDraftId = null;
        let selectedReworkId = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the page
            initializePage();

            // Add event listeners
            setupEventListeners();

            // Animate progress steps
            animateProgressSteps();

            // Auto-expand first option for better UX
            setTimeout(() => {
                toggleOptionCard('newJobCard');
            }, 500);
        });

        function initializePage() {
            // Clear any previous selections
            clearLocalStorage();

            // Ensure continue button is initially disabled
            const continueButton = document.getElementById('continueButton');
            if (continueButton) {
                continueButton.classList.add('disabled');
            }
        }

        function setupEventListeners() {
            // Add click listeners to option cards
            document.querySelectorAll('.primary-option-card').forEach(card => {
                const header = card.querySelector('.option-header');
                if (header) {
                    header.addEventListener('click', function() {
                        toggleOptionCard(card.id);
                    });
                }
            });

            // Close option cards when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.primary-option-card') && !event.target.closest('.help-modal')) {
                    // Don't close if clicking on help modal
                }
            });
        }

        function animateProgressSteps() {
            const steps = document.querySelectorAll('.step-number');
            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.classList.add('animate-pulse');
                    setTimeout(() => step.classList.remove('animate-pulse'), 600);
                }, index * 200);
            });
        }

        function clearLocalStorage() {
            // Clear previous selections
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('editedCategory_') || key.startsWith('editedTitle_') ||
                    key.startsWith('selected') || key.startsWith('project')) {
                    localStorage.removeItem(key);
                }
            });
        }

        function toggleOptionCard(cardId) {
            const card = document.getElementById(cardId);
            if (!card) return;

            const details = card.querySelector('.option-details');
            const toggle = card.querySelector('.option-toggle i');

            // Close other cards
            document.querySelectorAll('.primary-option-card').forEach(otherCard => {
                if (otherCard.id !== cardId) {
                    const otherDetails = otherCard.querySelector('.option-details');
                    const otherToggle = otherCard.querySelector('.option-toggle i');
                    if (otherDetails) {
                        otherDetails.style.display = 'none';
                        otherCard.classList.remove('expanded');
                    }
                    if (otherToggle) {
                        otherToggle.style.transform = 'rotate(0deg)';
                    }
                }
            });

            // Toggle current card
            if (details.style.display === 'none' || !details.style.display) {
                details.style.display = 'block';
                card.classList.add('expanded');
                toggle.style.transform = 'rotate(180deg)';

                // Animate the expansion
                setTimeout(() => {
                    details.style.opacity = '1';
                    details.style.transform = 'translateY(0)';
                }, 10);
            } else {
                details.style.opacity = '0';
                details.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    details.style.display = 'none';
                    card.classList.remove('expanded');
                }, 300);
                toggle.style.transform = 'rotate(0deg)';
            }
        }

        function selectJobType(jobType, element) {
            selectedJobType = jobType;
            selectedOption = 'new';

            // Clear other selections
            selectedDraftId = null;
            selectedReworkId = null;

            // Update UI
            document.querySelectorAll('.job-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            element.classList.add('selected');

            // Update hidden input
            const input = element.querySelector('input[type="radio"]');
            if (input) {
                input.checked = true;
            }

            // Store in localStorage
            localStorage.setItem('selected_job_type', jobType);
            localStorage.setItem('mode', 'create');

            // Enable continue button
            enableContinueButton(`Selected: ${jobType === 'one-time' ? 'One-time Project' : 'Ongoing Collaboration'}`);

            console.log('Selected job type:', jobType);
        }

        function selectDraft(element, title, draftId) {
            selectedOption = 'draft';
            selectedDraftId = draftId;

            // Clear other selections
            selectedJobType = null;
            selectedReworkId = null;

            // Update UI
            document.querySelectorAll('.draft-item').forEach(item => {
                item.classList.remove('selected');
            });
            element.classList.add('selected');

            // Store in localStorage
            localStorage.setItem('selectedJobTitle', title);
            localStorage.setItem('selectedDraftId', draftId);
            localStorage.setItem('mode', 'draft');

            // Update hidden input
            document.getElementById('selectedDraftId').value = draftId;

            // Enable continue button
            enableContinueButton(`Selected Draft: ${title}`);

            console.log('Selected draft:', title, 'ID:', draftId);
        }

        function selectRework(element, jobTitle, status, jobId) {
            selectedOption = 'rework';
            selectedReworkId = jobId;

            // Clear other selections
            selectedJobType = null;
            selectedDraftId = null;

            // Update UI
            document.querySelectorAll('.rework-item').forEach(item => {
                item.classList.remove('selected');
            });
            element.classList.add('selected');

            // Store in localStorage
            localStorage.setItem('selectedReworkJob', jobTitle);
            localStorage.setItem('selectedReworkId', jobId);
            localStorage.setItem('selectedReworkStatus', status);
            localStorage.setItem('mode', 'rework');

            // Update hidden input
            document.getElementById('selectedRework').value = jobId;

            // Enable continue button
            const statusText = status === 'paused' ? 'Paused' : 'Completed';
            enableContinueButton(`Selected ${statusText} Job: ${jobTitle}`);

            console.log('Selected rework job:', jobTitle, 'Status:', status, 'ID:', jobId);
        }

        function enableContinueButton(summaryText) {
            const continueButton = document.getElementById('continueButton');
            const selectionSummary = document.getElementById('selectionSummary');
            const summaryTextElement = document.getElementById('summaryText');

            // Enable button
            continueButton.classList.remove('disabled');
            continueButton.classList.add('enabled');

            // Show summary
            if (selectionSummary && summaryTextElement) {
                summaryTextElement.textContent = summaryText;
                selectionSummary.style.display = 'block';

                // Animate summary appearance
                setTimeout(() => {
                    selectionSummary.style.opacity = '1';
                    selectionSummary.style.transform = 'translateY(0)';
                }, 10);
            }

            // Add pulse animation to button
            continueButton.classList.add('pulse-animation');
            setTimeout(() => {
                continueButton.classList.remove('pulse-animation');
            }, 600);
        }

        function handleContinue() {
            if (!selectedOption) {
                showNotification('Please select an option to continue', 'warning');
                return;
            }

            // Show loading state
            showLoadingState();

            // Handle different selection types
            if (selectedOption === 'draft' && selectedDraftId) {
                // Redirect to page3 for draft editing
                setTimeout(() => {
                    window.location.href = `/page3?draft_job_id=${selectedDraftId}`;
                }, 1000);
            } else if (selectedOption === 'new' && selectedJobType) {
                // Store job type and redirect to page2
                localStorage.setItem('selected_job_type', selectedJobType);
                setTimeout(() => {
                    window.location.href = '/page2';
                }, 1000);
            } else if (selectedOption === 'rework' && selectedReworkId) {
                // Store rework job info and redirect to page2
                localStorage.setItem('selectedReworkId', selectedReworkId);
                localStorage.setItem('mode', 'rework');
                setTimeout(() => {
                    window.location.href = '/page2';
                }, 1000);
            } else {
                hideLoadingState();
                showNotification('Please make a selection to continue', 'error');
            }
        }

        function showLoadingState() {
            const continueButton = document.getElementById('continueButton');
            const buttonText = continueButton.querySelector('.button-text');
            const buttonIcon = continueButton.querySelector('i');

            continueButton.classList.add('loading');
            buttonText.textContent = 'Processing...';
            buttonIcon.className = 'fas fa-spinner fa-spin';
        }

        function hideLoadingState() {
            const continueButton = document.getElementById('continueButton');
            const buttonText = continueButton.querySelector('.button-text');
            const buttonIcon = continueButton.querySelector('i');

            continueButton.classList.remove('loading');
            buttonText.textContent = 'Continue';
            buttonIcon.className = 'fas fa-arrow-right';
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()"><i class="fas fa-times"></i></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        function cancelProcess() {
            // Create modal overlay
            const modalOverlay = document.createElement('div');
            modalOverlay.className = 'modal-overlay';
            modalOverlay.style.position = 'fixed';
            modalOverlay.style.top = '0';
            modalOverlay.style.left = '0';
            modalOverlay.style.width = '100%';
            modalOverlay.style.height = '100%';
            modalOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            modalOverlay.style.display = 'flex';
            modalOverlay.style.alignItems = 'center';
            modalOverlay.style.justifyContent = 'center';
            modalOverlay.style.zIndex = '1000';
            
            // Create modal content
            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';
            modalContent.style.backgroundColor = 'white';
            modalContent.style.borderRadius = '8px';
            modalContent.style.padding = '20px';
            modalContent.style.width = '90%';
            modalContent.style.maxWidth = '400px';
            modalContent.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
            
            // Create modal header
            const modalHeader = document.createElement('div');
            modalHeader.className = 'modal-header';
            modalHeader.style.display = 'flex';
            modalHeader.style.alignItems = 'center';
            modalHeader.style.marginBottom = '15px';
            
            const warningIcon = document.createElement('i');
            warningIcon.className = 'fas fa-exclamation-triangle';
            warningIcon.style.color = '#f59e0b';
            warningIcon.style.fontSize = '24px';
            warningIcon.style.marginRight = '10px';
            
            const modalTitle = document.createElement('h3');
            modalTitle.textContent = 'Confirm Cancellation';
            modalTitle.style.margin = '0';
            modalTitle.style.fontSize = '18px';
            modalTitle.style.fontWeight = '600';
            
            modalHeader.appendChild(warningIcon);
            modalHeader.appendChild(modalTitle);
            
            // Create modal body
            const modalBody = document.createElement('div');
            modalBody.className = 'modal-body';
            modalBody.style.marginBottom = '20px';
            
            const modalText = document.createElement('p');
            modalText.textContent = 'Are you sure you want to cancel? Any unsaved progress will be lost.';
            modalText.style.margin = '0';
            modalText.style.fontSize = '14px';
            modalText.style.color = '#4b5563';
            
            modalBody.appendChild(modalText);
            
            // Create modal footer with buttons
            const modalFooter = document.createElement('div');
            modalFooter.className = 'modal-footer';
            modalFooter.style.display = 'flex';
            modalFooter.style.justifyContent = 'flex-end';
            modalFooter.style.gap = '10px';
            
            const cancelButton = document.createElement('button');
            cancelButton.textContent = 'Stay Here';
            cancelButton.style.padding = '8px 16px';
            cancelButton.style.backgroundColor = 'white';
            cancelButton.style.color = '#4b5563';
            cancelButton.style.border = '1px solid #d1d5db';
            cancelButton.style.borderRadius = '4px';
            cancelButton.style.fontSize = '14px';
            cancelButton.style.fontWeight = '500';
            cancelButton.style.cursor = 'pointer';
            
            const confirmButton = document.createElement('button');
            confirmButton.textContent = 'Yes, Cancel';
            confirmButton.style.padding = '8px 16px';
            confirmButton.style.backgroundColor = '#ef4444';
            confirmButton.style.color = 'white';
            confirmButton.style.border = 'none';
            confirmButton.style.borderRadius = '4px';
            confirmButton.style.fontSize = '14px';
            confirmButton.style.fontWeight = '500';
            confirmButton.style.cursor = 'pointer';
            
            modalFooter.appendChild(cancelButton);
            modalFooter.appendChild(confirmButton);
            
            // Assemble modal
            modalContent.appendChild(modalHeader);
            modalContent.appendChild(modalBody);
            modalContent.appendChild(modalFooter);
            modalOverlay.appendChild(modalContent);
            
            // Add modal to body
            document.body.appendChild(modalOverlay);
            
            // Add event listeners
            cancelButton.addEventListener('click', function() {
                document.body.removeChild(modalOverlay);
            });
            
            confirmButton.addEventListener('click', function() {
                window.location.href = '/client_page';
            });
            
            // Close modal when clicking outside
            modalOverlay.addEventListener('click', function(event) {
                if (event.target === modalOverlay) {
                    document.body.removeChild(modalOverlay);
                }
            });
        }


    </script>
</body>
</html>